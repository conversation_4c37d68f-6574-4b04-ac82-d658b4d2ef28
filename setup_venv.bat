@echo off
chcp 65001 >nul
echo ========================================
echo        米家项目虚拟环境安装脚本
echo ========================================
echo.

:: 检查Python是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] 检测到Python版本:
python --version

:: 检查pip是否可用
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] pip未找到，请确保Python安装正确
    pause
    exit /b 1
)

echo [信息] 检测到pip版本:
pip --version
echo.

:: 创建虚拟环境
echo [步骤1] 创建虚拟环境...
if exist "venv" (
    echo [警告] 虚拟环境目录已存在，是否要删除重新创建？
    set /p choice="输入 y 删除重建，其他键跳过创建: "
    if /i "%choice%"=="y" (
        echo [信息] 删除现有虚拟环境...
        rmdir /s /q venv
        echo [信息] 创建新的虚拟环境...
        python -m venv venv
    ) else (
        echo [信息] 跳过虚拟环境创建
    )
) else (
    echo [信息] 创建虚拟环境...
    python -m venv venv
)

if %errorlevel% neq 0 (
    echo [错误] 虚拟环境创建失败
    pause
    exit /b 1
)

echo [信息] 虚拟环境创建成功
echo.

:: 激活虚拟环境并安装依赖
echo [步骤2] 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo [错误] 虚拟环境激活失败
    pause
    exit /b 1
)

echo [信息] 虚拟环境已激活
echo [信息] 升级pip到最新版本...
python -m pip install --upgrade pip

echo [信息] 安装项目依赖...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo [错误] 依赖安装失败，请检查requirements.txt文件和网络连接
    pause
    exit /b 1
)

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo [成功] 虚拟环境已创建并安装所有依赖
echo [提示] 使用 start_app.bat 启动应用
echo [提示] 手动激活虚拟环境: venv\Scripts\activate.bat
echo ========================================
echo.
pause
