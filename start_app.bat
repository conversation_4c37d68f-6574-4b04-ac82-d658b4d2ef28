@echo off
chcp 65001 >nul
echo ========================================
echo        米家项目启动脚本
echo ========================================
echo.

:: 检查虚拟环境是否存在
if not exist "venv" (
    echo [错误] 虚拟环境不存在！
    echo [提示] 请先运行 setup_venv.bat 创建虚拟环境
    pause
    exit /b 1
)

:: 检查app.py是否存在
if not exist "app.py" (
    echo [错误] app.py文件不存在！
    echo [提示] 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 激活虚拟环境
echo [信息] 激活虚拟环境...
call venv\Scripts\activate.bat

if %errorlevel% neq 0 (
    echo [错误] 虚拟环境激活失败
    echo [提示] 请重新运行 setup_venv.bat 重建虚拟环境
    pause
    exit /b 1
)

echo [信息] 虚拟环境已激活
echo [信息] Python版本: 
python --version
echo.

:: 检查依赖是否已安装
echo [信息] 检查项目依赖...
python -c "import flask, requests, mijiaAPI" >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] 部分依赖可能未安装，正在尝试安装...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
)

echo [信息] 依赖检查完成
echo.

:: 启动应用
echo ========================================
echo           启动Flask应用
echo ========================================
echo [信息] 正在启动 app.py...
echo [提示] 按 Ctrl+C 停止应用
echo [提示] 应用启动后会自动打开浏览器
echo ========================================
echo.

python app.py

:: 应用退出后的处理
echo.
echo ========================================
echo [信息] 应用已停止
echo ========================================
pause
